/**
 * 移动端优化视频播放器
 * <AUTHOR>
 * @version 2.0.0
 */

// 移动端优化的视频播放器
class MobileVideoPlayer {
    constructor(videoElement) {
        this.video = videoElement;
        this.loadingElement = document.getElementById('video-loading');

        // 从服务端获取设备检测结果
        this.isMobile = this.video.dataset.isMobile === 'true';
        this.isIOS = this.video.dataset.isIos === 'true';
        this.isAndroid = this.video.dataset.isAndroid === 'true';
        this.isLowEnd = this.video.dataset.isLowEnd === 'true';

        // 获取优化配置
        this.bufferSize = this.video.dataset.bufferSize || 'medium';
        this.videoQuality = this.video.dataset.videoQuality || 'auto';

        // 状态变量
        this.videoLoaded = false;
        this.bufferCheckInterval = null;
        this.networkStatus = this.detectNetworkStatus();

        this.init();
    }
    
    init() {
        this.setupMobileOptimizations();
        this.setupLazyLoading();
        this.setupEventListeners();
        this.checkNetworkStatus();
        this.setupNetworkStatusIndicator();
        this.setupBufferProgressBar();

        console.log('移动端优化视频播放器已初始化', {
            isMobile: this.isMobile,
            isIOS: this.isIOS,
            isAndroid: this.isAndroid,
            isLowEnd: this.isLowEnd,
            bufferSize: this.bufferSize,
            videoQuality: this.videoQuality,
            networkStatus: this.networkStatus
        });
    }
    
    // 移动端优化配置
    setupMobileOptimizations() {
        if (this.isMobile) {
            // 移动端预加载策略：仅加载元数据
            this.video.preload = 'metadata';
            
            // iOS特殊处理
            if (this.isIOS) {
                this.video.setAttribute('webkit-playsinline', 'true');
                this.video.setAttribute('playsinline', 'true');
                // iOS Safari 优化
                this.video.style.webkitTransform = 'translateZ(0)';
                this.video.style.transform = 'translateZ(0)';
            }
            
            // 安卓微信/QQ浏览器优化
            if (this.isAndroid) {
                this.video.setAttribute('x5-video-player-type', 'h5');
                this.video.setAttribute('x5-video-player-fullscreen', 'true');
                this.video.setAttribute('x5-video-orientation', 'portraint');
                this.video.setAttribute('x5-playsinline', 'true');
            }
            
            // 移动端性能优化
            this.video.style.willChange = 'transform';
            this.video.style.backfaceVisibility = 'hidden';
        } else {
            // 桌面端可以预加载更多
            this.video.preload = 'auto';
        }
    }
    
    // 延迟加载设置
    setupLazyLoading() {
        const videoUrl = this.video.dataset.videoUrl;
        
        if (this.isMobile && videoUrl) {
            // 移动端：添加播放按钮，用户交互后再加载视频
            this.createPlayButton();
        } else if (videoUrl) {
            // 桌面端直接加载
            this.loadVideoSource();
        }
    }
    
    // 创建移动端播放按钮
    createPlayButton() {
        const playButton = document.createElement('div');
        playButton.className = 'mobile-play-button';
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: rgba(0,0,0,0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            z-index: 100;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 2px solid rgba(255,255,255,0.3);
        `;
        
        this.video.parentNode.appendChild(playButton);
        
        // 点击播放按钮
        playButton.addEventListener('click', () => {
            this.loadVideoSource();
            playButton.style.display = 'none';
            setTimeout(() => {
                this.video.play().catch(e => console.log('自动播放被阻止:', e));
            }, 100);
        });
        
        // 点击视频也能触发加载
        this.video.addEventListener('click', () => {
            if (!this.videoLoaded) {
                this.loadVideoSource();
                playButton.style.display = 'none';
            }
        });
        
        this.playButton = playButton;
    }
    
    // 加载视频源
    loadVideoSource() {
        const videoUrl = this.video.dataset.videoUrl;
        if (videoUrl && !this.videoLoaded) {
            this.video.src = videoUrl;
            this.videoLoaded = true;
            console.log('视频源已加载');
        }
    }
    
    // 加载状态管理
    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'none';
        }
    }
    
    showLoading() {
        if (this.loadingElement) {
            this.loadingElement.style.display = 'flex';
        }
    }
    
    // 智能缓冲检测
    startBufferCheck() {
        if (this.bufferCheckInterval) {
            clearInterval(this.bufferCheckInterval);
        }

        this.bufferCheckInterval = setInterval(() => {
            if (this.video.buffered.length > 0) {
                const bufferedEnd = this.video.buffered.end(this.video.buffered.length - 1);
                const duration = this.video.duration;

                if (duration > 0) {
                    const bufferedPercent = (bufferedEnd / duration) * 100;

                    // 更新缓冲进度条
                    this.updateBufferProgress();

                    // 根据设备和网络状态调整阈值
                    let threshold;
                    if (this.isLowEnd) {
                        threshold = 25; // 低端设备需要更多缓冲
                    } else if (this.isMobile) {
                        threshold = this.networkStatus.effectiveType === '4g' ? 10 : 20;
                    } else {
                        threshold = 8; // 桌面端
                    }

                    if (bufferedPercent > threshold) {
                        this.hideLoading();
                        clearInterval(this.bufferCheckInterval);
                        this.bufferCheckInterval = null;
                    }
                }
            }
        }, this.isLowEnd ? 500 : 200); // 低端设备降低检测频率
    }
    
    // 检测网络状态
    detectNetworkStatus() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            return {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData
            };
        }
        return { effectiveType: 'unknown' };
    }

    // 网络状态检测（移动端优化）
    checkNetworkStatus() {
        if (this.isMobile && 'connection' in navigator) {
            const connection = navigator.connection;
            const effectiveType = connection.effectiveType;

            // 根据网络状态调整预加载策略
            switch (effectiveType) {
                case 'slow-2g':
                case '2g':
                    this.video.preload = 'none';
                    this.updateNetworkStatusIndicator('慢速网络', 'slow');
                    console.log('检测到慢速网络，禁用预加载');
                    break;
                case '3g':
                    this.video.preload = 'metadata';
                    this.updateNetworkStatusIndicator('3G网络', 'medium');
                    console.log('检测到3G网络，仅预加载元数据');
                    break;
                case '4g':
                default:
                    this.video.preload = this.isLowEnd ? 'metadata' : 'auto';
                    this.updateNetworkStatusIndicator('快速网络', 'fast');
                    console.log('检测到快速网络');
                    break;
            }

            // 监听网络状态变化
            connection.addEventListener('change', () => {
                this.networkStatus = this.detectNetworkStatus();
                this.checkNetworkStatus();
            });
        }
    }

    // 设置网络状态指示器
    setupNetworkStatusIndicator() {
        if (this.isMobile) {
            const indicator = document.createElement('div');
            indicator.className = 'network-status';
            indicator.id = 'network-status';
            this.video.parentNode.appendChild(indicator);
            this.networkIndicator = indicator;
        }
    }

    // 更新网络状态指示器
    updateNetworkStatusIndicator(text, status) {
        if (this.networkIndicator) {
            this.networkIndicator.textContent = text;
            this.networkIndicator.className = `network-status ${status}`;
            this.networkIndicator.style.display = 'block';

            // 3秒后隐藏
            setTimeout(() => {
                if (this.networkIndicator) {
                    this.networkIndicator.style.display = 'none';
                }
            }, 3000);
        }
    }

    // 设置缓冲进度条
    setupBufferProgressBar() {
        if (this.isMobile) {
            const progressContainer = document.createElement('div');
            progressContainer.className = 'buffer-progress';

            const progressBar = document.createElement('div');
            progressBar.className = 'buffer-progress-bar';
            progressBar.id = 'buffer-progress-bar';

            progressContainer.appendChild(progressBar);
            this.video.parentNode.appendChild(progressContainer);
            this.bufferProgressBar = progressBar;
        }
    }

    // 更新缓冲进度条
    updateBufferProgress() {
        if (this.bufferProgressBar && this.video.buffered.length > 0) {
            const bufferedEnd = this.video.buffered.end(this.video.buffered.length - 1);
            const duration = this.video.duration;

            if (duration > 0) {
                const bufferedPercent = (bufferedEnd / duration) * 100;
                this.bufferProgressBar.style.width = `${bufferedPercent}%`;
            }
        }
    }
    
    // 设置事件监听器
    setupEventListeners() {
        // 加载开始
        this.video.addEventListener('loadstart', () => {
            console.log('开始加载视频');
            this.showLoading();
            this.startBufferCheck();
        });
        
        // 可以播放
        this.video.addEventListener('canplay', () => {
            console.log('视频可以播放');
            this.hideLoading();
            if (this.bufferCheckInterval) {
                clearInterval(this.bufferCheckInterval);
                this.bufferCheckInterval = null;
            }
        });
        
        // 等待缓冲
        this.video.addEventListener('waiting', () => {
            console.log('视频缓冲中...');
            this.showLoading();
            this.startBufferCheck();
        });

        // 开始播放
        this.video.addEventListener('playing', () => {
            console.log('视频正在播放');
            this.hideLoading();
        });

        // 进度更新
        this.video.addEventListener('progress', () => {
            this.updateBufferProgress();
        });

        // 时间更新
        this.video.addEventListener('timeupdate', () => {
            // 定期更新缓冲进度
            if (this.video.currentTime % 5 < 0.1) { // 每5秒更新一次
                this.updateBufferProgress();
            }
        });
        
        // 播放开始
        this.video.addEventListener('play', () => {
            const videoId = this.video.dataset.videoId;
            console.log('视频开始播放，ID:', videoId);
        });
        
        // 播放结束
        this.video.addEventListener('ended', () => {
            const videoId = this.video.dataset.videoId;
            console.log('视频播放结束，ID:', videoId);
        });
        
        // 错误处理
        this.video.addEventListener('error', (e) => {
            console.error('视频播放出错:', e);
            this.hideLoading();
            this.handleVideoError();
        });
        
        // 移动端触摸优化
        if (this.isMobile) {
            this.setupTouchOptimizations();
        }
    }
    
    // 处理视频错误
    handleVideoError() {
        let errorMessage = '视频加载失败';
        
        if (this.video.error) {
            switch (this.video.error.code) {
                case 1: errorMessage = '视频加载被中止'; break;
                case 2: errorMessage = '网络错误，请检查网络连接'; break;
                case 3: errorMessage = '视频格式不支持或解码失败'; break;
                case 4: errorMessage = '视频不存在或无法访问'; break;
            }
        }
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger mt-3';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
        this.video.parentNode.appendChild(errorDiv);
    }
    
    // 移动端触摸优化
    setupTouchOptimizations() {
        let touchStartTime = 0;
        
        this.video.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });
        
        this.video.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            
            // 短触摸（小于300ms）切换播放/暂停
            if (touchDuration < 300) {
                if (this.video.paused) {
                    this.video.play();
                } else {
                    this.video.pause();
                }
            }
        });
    }
    
    // 销毁播放器
    destroy() {
        if (this.bufferCheckInterval) {
            clearInterval(this.bufferCheckInterval);
        }
        
        if (this.playButton) {
            this.playButton.remove();
        }
        
        console.log('移动端视频播放器已销毁');
    }
}

// 初始化播放器
document.addEventListener('DOMContentLoaded', function() {
    const videoElement = document.getElementById('video-player');
    if (videoElement) {
        window.mobilePlayer = new MobileVideoPlayer(videoElement);
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (window.mobilePlayer) {
        window.mobilePlayer.destroy();
    }
});
