# 移动端优化视频播放器部署指南

## 🚀 快速部署

### 1. 环境要求
- **Java**: 17+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **浏览器**: 支持HTML5视频的现代浏览器

### 2. 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE video_player CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始化脚本
mysql -u root -p video_player < database/init.sql
```

### 3. 应用配置
```yaml
# src/main/resources/application.yml
spring:
  datasource:
    url: *************************************************************************************************************************
    username: root
    password: your_password
```

### 4. 编译和运行
```bash
# 编译项目
mvn clean package

# 运行应用
java -jar target/video-player-1.0.0.jar

# 或者直接运行
mvn spring-boot:run
```

### 5. 访问应用
- **本地访问**: http://localhost:5000
- **视频播放**: http://localhost:5000/play/{video_id}
- **管理后台**: http://localhost:5000/admin

## 📱 移动端测试

### 1. 模拟移动设备
在Chrome浏览器中：
1. 按F12打开开发者工具
2. 点击设备模拟图标（手机/平板图标）
3. 选择移动设备型号（iPhone、Android等）
4. 刷新页面测试移动端优化效果

### 2. 真机测试
1. 确保手机和电脑在同一网络
2. 获取电脑IP地址：`ipconfig`（Windows）或`ifconfig`（Mac/Linux）
3. 手机浏览器访问：`http://你的IP:5000`

### 3. 测试要点
- **加载速度**: 观察视频加载时间
- **播放流畅度**: 检查是否有卡顿
- **网络适应**: 在不同网络环境下测试
- **设备兼容**: 在不同品牌手机上测试

## 🔧 配置优化

### 1. 生产环境配置
```yaml
# application-prod.yml
spring:
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.videoplayer: WARN
    org.springframework: WARN

server:
  compression:
    enabled: true
    mime-types: text/html,text/css,application/javascript,application/json
```

### 2. 移动端专用配置
```yaml
# 移动端文件上传限制
spring:
  servlet:
    multipart:
      max-file-size: 50MB  # 移动端网络较慢，适当降低
      max-request-size: 50MB
```

### 3. 缓存配置
```yaml
# 静态资源缓存
spring:
  web:
    resources:
      cache:
        cachecontrol:
          max-age: 86400  # 24小时
```

## 🌐 CDN和静态资源优化

### 1. 使用CDN
```html
<!-- 替换本地Bootstrap和FontAwesome -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
```

### 2. 静态资源压缩
```bash
# 压缩CSS和JS文件
npm install -g uglifycss uglify-js

# 压缩CSS
uglifycss src/main/resources/static/css/*.css > compressed.css

# 压缩JS
uglifyjs src/main/resources/static/js/*.js -o compressed.js
```

## 🐳 Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY target/video-player-1.0.0.jar app.jar

EXPOSE 5000

CMD ["java", "-jar", "app.jar"]
```

### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - SPRING_DATASOURCE_URL=*********************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=root
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=video_player
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql

volumes:
  mysql_data:
```

### 3. 部署命令
```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 📊 性能监控

### 1. 应用监控
```yaml
# 启用Actuator监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

### 2. 数据库监控
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

### 3. 前端性能监控
```javascript
// 添加到mobile-player.js
console.log('视频加载性能:', {
    loadTime: performance.now(),
    networkType: navigator.connection?.effectiveType,
    deviceMemory: navigator.deviceMemory
});
```

## 🔒 安全配置

### 1. HTTPS配置
```yaml
# application.yml
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: your_password
    key-store-type: PKCS12
```

### 2. 跨域配置
```java
// WebConfig.java 已包含CORS配置
@Override
public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS");
}
```

## 🚨 故障排除

### 1. 常见问题

#### 视频无法播放
- 检查视频URL是否可访问
- 确认视频格式是否支持（推荐MP4）
- 查看浏览器控制台错误信息

#### 移动端卡顿
- 检查网络状态指示器
- 查看缓冲进度条
- 确认设备检测是否正确

#### 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查端口是否开放
netstat -tlnp | grep 3306
```

### 2. 日志分析
```bash
# 查看应用日志
tail -f logs/spring.log

# 查看错误日志
grep ERROR logs/spring.log
```

### 3. 性能调优
```yaml
# JVM参数优化
java -Xms512m -Xmx1024m -XX:+UseG1GC -jar app.jar
```

## 📈 扩展功能

### 1. 视频转码
```bash
# 使用FFmpeg转码
ffmpeg -i input.avi -c:v libx264 -c:a aac -f mp4 output.mp4
```

### 2. 缩略图生成
```bash
# 生成视频缩略图
ffmpeg -i video.mp4 -ss 00:00:01 -vframes 1 thumbnail.jpg
```

### 3. 自适应码率
```javascript
// 根据网络状况切换视频质量
if (connection.effectiveType === '2g') {
    video.src = lowQualityUrl;
} else if (connection.effectiveType === '4g') {
    video.src = highQualityUrl;
}
```

## 📞 技术支持

如遇到问题，请：
1. 查看控制台错误信息
2. 检查网络连接状态
3. 确认浏览器兼容性
4. 查看服务器日志

---

**祝您部署顺利！享受流畅的移动端视频播放体验！** 🎉
