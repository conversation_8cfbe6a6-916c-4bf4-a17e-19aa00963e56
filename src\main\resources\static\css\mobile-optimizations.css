/**
 * 移动端视频播放器优化样式
 * Mobile Video Player Optimizations
 * @version 2.0.0
 */

/* 移动端性能优化基础样式 */
.mobile-optimized {
    /* GPU加速 */
    will-change: transform;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    
    /* 减少重绘 */
    contain: layout style paint;
    
    /* 优化滚动 */
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* 视频元素移动端优化 */
video {
    /* 硬件加速 */
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    
    /* 移动端播放优化 */
    -webkit-playsinline: true;
    -moz-playsinline: true;
    playsinline: true;
    
    /* 禁用移动端双击缩放 */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    
    /* 优化视频渲染 */
    object-fit: contain;
    object-position: center;
}

/* 移动端容器优化 */
@media (max-width: 768px) {
    .video-player-container {
        /* 性能优化 */
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        contain: layout style paint;
        
        /* 减少内存使用 */
        isolation: isolate;
    }
    
    .video-wrapper {
        /* 滚动优化 */
        -webkit-overflow-scrolling: touch;
        overflow: hidden;
        
        /* 减少重排 */
        contain: layout;
    }
    
    /* 移动端视频特殊优化 */
    #video-player {
        /* 3D变换优化 */
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        
        /* 减少重绘 */
        will-change: transform;
        backface-visibility: hidden;
        
        /* 移动端触摸优化 */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
}

/* 小屏幕设备优化 */
@media (max-width: 576px) {
    .container-fluid {
        /* 减少内存占用 */
        contain: layout style;
    }
    
    .video-player-container {
        /* 小屏幕性能优化 */
        contain: layout style paint;
        isolation: isolate;
    }
    
    .video-wrapper {
        /* 小屏幕滚动优化 */
        -webkit-overflow-scrolling: touch;
        contain: layout;
    }
    
    #video-player {
        /* 小屏幕视频优化 */
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        
        /* 优化触摸响应 */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
    }
}

/* 移动端播放按钮优化 */
.mobile-play-button {
    /* 硬件加速 */
    will-change: transform, opacity;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    
    /* 减少重绘 */
    contain: layout style paint;
    isolation: isolate;
    
    /* 触摸优化 */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    
    /* 视觉效果 */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    
    /* 动画优化 */
    transition: transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.2s ease,
                background-color 0.2s ease;
}

.mobile-play-button:hover {
    -webkit-transform: translate3d(-50%, -50%, 0) scale(1.05);
    transform: translate3d(-50%, -50%, 0) scale(1.05);
}

.mobile-play-button:active {
    -webkit-transform: translate3d(-50%, -50%, 0) scale(0.95);
    transform: translate3d(-50%, -50%, 0) scale(0.95);
}

/* 加载状态优化 */
.video-loading {
    /* 硬件加速 */
    will-change: opacity;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    
    /* 减少重绘 */
    contain: layout style paint;
    
    /* 背景优化 */
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
}

.video-loading .spinner-border {
    /* 动画优化 */
    will-change: transform;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* 网络状态指示器 */
.network-status {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 200;
    display: none;
}

.network-status.slow {
    background: rgba(255,193,7,0.9);
    color: #000;
}

.network-status.fast {
    background: rgba(40,167,69,0.9);
}

/* 缓冲进度条 */
.buffer-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255,255,255,0.3);
    width: 100%;
    z-index: 150;
}

.buffer-progress-bar {
    height: 100%;
    background: rgba(0,123,255,0.8);
    width: 0%;
    transition: width 0.3s ease;
}

/* iOS Safari 特殊优化 */
@supports (-webkit-touch-callout: none) {
    video {
        /* iOS Safari 视频优化 */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
    
    .video-wrapper {
        /* iOS Safari 容器优化 */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
}

/* Android WebView 优化 */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    video {
        /* Android 视频优化 */
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-play-button {
        /* 高DPI屏幕图标优化 */
        font-size: 26px;
    }
    
    .video-loading .spinner-border {
        /* 高DPI屏幕加载动画优化 */
        border-width: 3px;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
    .video-player-container {
        max-height: 90vh;
    }
    
    .video-wrapper {
        padding-bottom: 0;
        height: 90vh;
    }
    
    #video-player {
        position: static;
        width: 100%;
        height: 100%;
    }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
    .mobile-play-button,
    .video-loading,
    .buffer-progress-bar {
        transition: none;
        animation: none;
    }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .mobile-play-button {
        background: rgba(255,255,255,0.1);
        border-color: rgba(255,255,255,0.2);
    }
    
    .network-status {
        background: rgba(255,255,255,0.1);
        color: #fff;
    }
}
