package com.videoplayer;

import com.videoplayer.config.MobileOptimizationConfig;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 移动端优化功能测试
 */
public class MobileOptimizationTest {

    private final MobileOptimizationConfig.MobileDetectionInterceptor interceptor = 
        new MobileOptimizationConfig.MobileDetectionInterceptor();

    @Test
    public void testMobileDeviceDetection() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试iPhone设备检测
        request.addHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");
        
        boolean result = interceptor.preHandle(request, response, null);
        
        assertTrue(result);
        assertTrue((Boolean) request.getAttribute("isMobile"));
        assertTrue((Boolean) request.getAttribute("isIOS"));
        assertFalse((Boolean) request.getAttribute("isAndroid"));
    }

    @Test
    public void testAndroidDeviceDetection() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试Android设备检测
        request.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36");
        
        boolean result = interceptor.preHandle(request, response, null);
        
        assertTrue(result);
        assertTrue((Boolean) request.getAttribute("isMobile"));
        assertFalse((Boolean) request.getAttribute("isIOS"));
        assertTrue((Boolean) request.getAttribute("isAndroid"));
    }

    @Test
    public void testDesktopDeviceDetection() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试桌面设备检测
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        
        boolean result = interceptor.preHandle(request, response, null);
        
        assertTrue(result);
        // 桌面设备应该不被识别为移动设备
        assertNull(request.getAttribute("isMobile"));
    }

    @Test
    public void testLowEndDeviceDetection() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试低端设备检测
        request.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 4.4.2; SM-G7102) AppleWebKit/537.36");
        
        boolean result = interceptor.preHandle(request, response, null);
        
        assertTrue(result);
        assertTrue((Boolean) request.getAttribute("isMobile"));
        assertTrue((Boolean) request.getAttribute("isAndroid"));
        assertTrue((Boolean) request.getAttribute("isLowEnd"));
    }

    @Test
    public void testMobileOptimizationSettings() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试移动端优化设置
        request.addHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15");
        
        interceptor.preHandle(request, response, null);
        
        // 验证iOS特殊设置
        assertTrue((Boolean) request.getAttribute("enablePlaysinline"));
        assertTrue((Boolean) request.getAttribute("enableWebkitPlaysinline"));
        assertTrue((Boolean) request.getAttribute("iosOptimized"));
        assertTrue((Boolean) request.getAttribute("enableMobileOptimizations"));
    }

    @Test
    public void testAndroidOptimizationSettings() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试Android优化设置
        request.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36");
        
        interceptor.preHandle(request, response, null);
        
        // 验证Android特殊设置
        assertTrue((Boolean) request.getAttribute("enableX5Player"));
        assertEquals("h5", request.getAttribute("x5VideoPlayerType"));
        assertTrue((Boolean) request.getAttribute("x5VideoPlayerFullscreen"));
        assertTrue((Boolean) request.getAttribute("androidOptimized"));
    }

    @Test
    public void testLowEndDeviceOptimizations() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试低端设备优化
        request.addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 4.4.2; SM-G7102) AppleWebKit/537.36");
        
        interceptor.preHandle(request, response, null);
        
        // 验证低端设备设置
        assertEquals("none", request.getAttribute("videoPreload"));
        assertEquals("small", request.getAttribute("bufferSize"));
        
        // 验证缓存策略
        assertEquals("public, max-age=86400", response.getHeader("Cache-Control"));
    }

    @Test
    public void testHighEndMobileOptimizations() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试高端移动设备优化
        request.addHeader("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15");
        
        interceptor.preHandle(request, response, null);
        
        // 验证高端设备设置
        assertEquals("metadata", request.getAttribute("videoPreload"));
        assertEquals("medium", request.getAttribute("bufferSize"));
        
        // 验证缓存策略
        assertEquals("public, max-age=43200", response.getHeader("Cache-Control"));
    }

    @Test
    public void testNullUserAgent() {
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 测试空User-Agent
        // 不设置User-Agent头
        
        boolean result = interceptor.preHandle(request, response, null);
        
        assertTrue(result);
        // 应该没有设置任何移动端属性
        assertNull(request.getAttribute("isMobile"));
        assertNull(request.getAttribute("isIOS"));
        assertNull(request.getAttribute("isAndroid"));
    }
}
