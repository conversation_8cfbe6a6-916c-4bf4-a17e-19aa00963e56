<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <!-- 移动端优化 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <!-- 预连接优化 -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
    <!-- 移动端优化CSS -->
    <link href="/css/mobile-optimizations.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 移动端优化视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            th:attr="preload=${videoPreload ?: 'metadata'}"
                            th:classappend="${isMobile ? 'mobile-optimized' : ''}"
                            th:attrappend="playsinline=${enablePlaysinline ? 'true' : null}"
                            th:attrappend="webkit-playsinline=${enableWebkitPlaysinline ? 'true' : null}"
                            th:attrappend="x5-video-player-type=${enableX5Player ? x5VideoPlayerType : null}"
                            th:attrappend="x5-video-player-fullscreen=${enableX5Player ? x5VideoPlayerFullscreen : null}"
                            th:attrappend="x5-video-orientation=${enableX5Player ? 'portraint' : null}"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:data-is-mobile="${isMobile}"
                            th:data-is-ios="${isIOS}"
                            th:data-is-android="${isAndroid}"
                            th:data-is-low-end="${isLowEnd}"
                            th:data-buffer-size="${bufferSize}"
                            th:data-video-quality="${videoQuality}"
                            th:poster="${video.thumbnailUrl}"
                            crossorigin="anonymous">
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            您的浏览器不支持HTML5视频播放。
                        </video>
                        <!-- 加载状态 -->
                        <div class="video-loading" id="video-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>








                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们&nbsp;&nbsp;&nbsp;</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <!-- 移动端优化播放器 -->
    <script src="/js/mobile-player.js"></script>


    <!-- 移动端优化播放器已通过外部JS文件加载，无需重复代码 -->











</body>
</html>

