package com.videoplayer.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.regex.Pattern;

/**
 * 移动端优化配置
 * 检测移动设备并添加相应的优化配置
 */
@Configuration
public class MobileOptimizationConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new MobileDetectionInterceptor());
    }

    /**
     * 移动设备检测拦截器
     */
    public static class MobileDetectionInterceptor implements HandlerInterceptor {
        
        // 移动设备User-Agent正则表达式
        private static final Pattern MOBILE_PATTERN = Pattern.compile(
            "(?i)(android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|mobile)",
            Pattern.CASE_INSENSITIVE
        );
        
        // iOS设备检测
        private static final Pattern IOS_PATTERN = Pattern.compile(
            "(?i)(ipad|iphone|ipod)",
            Pattern.CASE_INSENSITIVE
        );
        
        // Android设备检测
        private static final Pattern ANDROID_PATTERN = Pattern.compile(
            "(?i)android",
            Pattern.CASE_INSENSITIVE
        );
        
        // 低端设备检测（基于User-Agent中的关键词）
        private static final Pattern LOW_END_PATTERN = Pattern.compile(
            "(?i)(android 4|android 5|iphone 5|iphone 6|samsung-gt|nokia|blackberry)",
            Pattern.CASE_INSENSITIVE
        );

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
            String userAgent = request.getHeader("User-Agent");
            
            if (userAgent != null) {
                // 检测设备类型
                boolean isMobile = MOBILE_PATTERN.matcher(userAgent).find();
                boolean isIOS = IOS_PATTERN.matcher(userAgent).find();
                boolean isAndroid = ANDROID_PATTERN.matcher(userAgent).find();
                boolean isLowEnd = LOW_END_PATTERN.matcher(userAgent).find();
                
                // 设置请求属性
                request.setAttribute("isMobile", isMobile);
                request.setAttribute("isIOS", isIOS);
                request.setAttribute("isAndroid", isAndroid);
                request.setAttribute("isLowEnd", isLowEnd);
                
                // 根据设备类型设置优化策略
                if (isMobile) {
                    setMobileOptimizations(request, response, isIOS, isAndroid, isLowEnd);
                }
            }
            
            return true;
        }
        
        /**
         * 设置移动端优化配置
         */
        private void setMobileOptimizations(HttpServletRequest request, HttpServletResponse response, 
                                          boolean isIOS, boolean isAndroid, boolean isLowEnd) {
            
            // 设置移动端缓存策略
            if (isLowEnd) {
                // 低端设备：更激进的缓存策略
                response.setHeader("Cache-Control", "public, max-age=86400"); // 24小时
                request.setAttribute("videoPreload", "none");
                request.setAttribute("bufferSize", "small");
            } else {
                // 高端移动设备：适中的缓存策略
                response.setHeader("Cache-Control", "public, max-age=43200"); // 12小时
                request.setAttribute("videoPreload", "metadata");
                request.setAttribute("bufferSize", "medium");
            }
            
            // iOS特殊优化
            if (isIOS) {
                request.setAttribute("enablePlaysinline", true);
                request.setAttribute("enableWebkitPlaysinline", true);
                // iOS Safari 视频优化
                request.setAttribute("iosOptimized", true);
            }
            
            // Android特殊优化
            if (isAndroid) {
                request.setAttribute("enableX5Player", true);
                request.setAttribute("x5VideoPlayerType", "h5");
                request.setAttribute("x5VideoPlayerFullscreen", true);
                // Android WebView 优化
                request.setAttribute("androidOptimized", true);
            }
            
            // 设置移动端视频质量策略
            String connectionType = request.getHeader("Connection");
            if (connectionType != null && connectionType.toLowerCase().contains("slow")) {
                request.setAttribute("videoQuality", "low");
                request.setAttribute("videoPreload", "none");
            } else {
                request.setAttribute("videoQuality", "auto");
            }
            
            // 移动端性能优化标记
            request.setAttribute("enableMobileOptimizations", true);
            request.setAttribute("enableHardwareAcceleration", true);
            request.setAttribute("enableTouchOptimizations", true);
        }
    }
}
