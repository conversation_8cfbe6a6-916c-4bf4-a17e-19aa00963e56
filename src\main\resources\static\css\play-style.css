/**
 * 视频播放页面专用样式 - 响应式设计
 * Video Play Page Styles - Responsive Design
 */

/* 移动端优化视频播放器样式 */
#video-player {
    width: 100%;
    height: auto;
    max-width: 100%;
    background-color: #000;
    border-radius: 8px;
    object-fit: contain;
    display: block;
    /* 移动端性能优化 */
    will-change: transform;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* 移动端播放优化 */
    -webkit-playsinline: true;
    -moz-playsinline: true;
    playsinline: true;
}

/* 播放器容器 - 响应式 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid #e9ecef;
}

/* 视频包装器 - 16:9 宽高比 */
.video-wrapper {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 宽高比 */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper #video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1px 0px !important;
}

/* 覆盖Bootstrap的mb-4类，确保margin设置生效 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    margin: 1px 0px !important;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* 特定选择器：确保video-title h3 mb-3的内容居中 */
.video-title.h3.mb-3 {
    text-align: center !important;
}


/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* 移动端优化样式 */
@media (max-width: 768px) {
    .video-player-container {
        margin: 0 auto 1rem;
        border-radius: 8px;
        max-width: 100%;
        /* 移动端性能优化 */
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
        /* 移动端滚动优化 */
        -webkit-overflow-scrolling: touch;
        overflow: hidden;
    }

    #video-player {
        /* 移动端视频优化 */
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        /* 禁用移动端双击缩放 */
        touch-action: manipulation;
    }

    .video-info {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
    }

    .video-title {
        font-size: 1.25rem;
        text-align: center;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .video-actions {
        width: 100%;
        text-align: center;
    }
}

/* 移动端播放按钮样式 */
.mobile-play-button {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 80px !important;
    height: 80px !important;
    background: rgba(0,0,0,0.7) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 24px !important;
    cursor: pointer !important;
    z-index: 100 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(5px) !important;
    border: 2px solid rgba(255,255,255,0.3) !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3) !important;
}

.mobile-play-button:hover {
    background: rgba(0,0,0,0.9) !important;
    transform: translate(-50%, -50%) scale(1.1) !important;
}

.mobile-play-button:active {
    transform: translate(-50%, -50%) scale(0.95) !important;
}

/* 加载状态 */
.video-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    z-index: 10;
}

.video-loading .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

/* 错误状态 */
.video-error {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}


/* 平板设备优化 */
@media (max-width: 991.98px) {
    .video-player-container {
        max-width: 500px;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
    }
}

@media (max-width: 576px) {
    /* 小屏幕设备完全响应式 */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .video-player-container {
        margin: 0 0 1rem 0;
        border-radius: 8px;
        max-width: 100%;
        /* 小屏幕性能优化 */
        contain: layout style paint;
    }

    .video-wrapper {
        padding-bottom: 56.25%; /* 保持16:9比例 */
        /* 小屏幕滚动优化 */
        -webkit-overflow-scrolling: touch;
    }

    #video-player {
        /* 小屏幕视频优化 */
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        /* 优化触摸响应 */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    .video-info {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }

    .video-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .video-description {
        font-size: 0.9rem;
    }

    /* 小屏幕播放按钮调整 */
    .mobile-play-button {
        width: 60px !important;
        height: 60px !important;
        font-size: 20px !important;
    }
}

/* 主容器样式调整 */
.container.my-4 {
    margin-top: 10px !important;
}

/* 页脚样式调整 */
.bg-dark.text-light.py-4.mt-5 {
    margin-top: 5px !important;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-title {
        color: #f7fafc;
    }


}



/* 视频描述样式 */
.video-description {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.video-description p {
    line-height: 1.6;
    color: #6c757d;
}

.video-wrapper {
    height: 380px!important;
    width: 100%!important;
}